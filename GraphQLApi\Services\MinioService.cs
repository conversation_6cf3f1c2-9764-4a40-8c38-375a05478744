using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Minio.Exceptions;
using GraphQLApi.Data;
using Shared.Configuration;
using Shared.Constants;
using Shared.Enums;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    /// <summary>
    /// MinIO service implementation for file storage operations
    /// </summary>
    public class MinioService : IMinioService
    {
        private readonly IMinioClient _minioClient;
        private readonly MinIOConfiguration _config;
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly ILogger<MinioService> _logger;

        public MinioService(
            IMinioClient minioClient,
            IOptions<MinIOConfiguration> config,
            IDbContextFactory<AppDbContext> dbContextFactory,
            ILogger<MinioService> logger)
        {
            _minioClient = minioClient;
            _config = config.Value;
            _dbContextFactory = dbContextFactory;
            _logger = logger;
        }

        public async Task<FileMetadata> UploadFileAsync(
            Stream stream,
            string fileName,
            string bucketName,
            string contentType,
            string? description = null,
            string? folderPath = null,
            bool isPublic = false,
            DateTime? expiresAt = null)
        {
            try
            {
                // Validate file type
                var validation = await ValidateFileTypeAsync(fileName, contentType);
                if (!validation.IsValid)
                {
                    throw new ArgumentException(validation.ErrorMessage);
                }

                // Validate file size
                if (stream.Length > _config.Settings.MaxFileSize)
                {
                    throw new ArgumentException($"File size exceeds maximum allowed size of {_config.Settings.MaxFileSize} bytes");
                }

                // Generate unique object key
                var objectKey = GenerateObjectKey(fileName, folderPath);
                var fullObjectPath = string.IsNullOrEmpty(folderPath) ? objectKey : $"{folderPath.TrimEnd('/')}/{objectKey}";

                // Ensure bucket exists
                await EnsureBucketExistsAsync(bucketName);

                // Upload to MinIO
                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(fullObjectPath)
                    .WithStreamData(stream)
                    .WithObjectSize(stream.Length)
                    .WithContentType(contentType);

                var response = await _minioClient.PutObjectAsync(putObjectArgs);

                // Create metadata record
                var fileMetadata = new FileMetadata
                {
                    FileName = fileName,
                    ContentType = contentType,
                    Size = stream.Length,
                    Description = description,
                    BucketName = bucketName,
                    ObjectKey = fullObjectPath,
                    ETag = response.Etag,
                    Version = response.VersionId,
                    FileType = validation.FileType!.Value,
                    FolderPath = folderPath,
                    IsPublic = isPublic,
                    ExpiresAt = expiresAt
                };

                // Save to database
                await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
                dbContext.FileMetadata.Add(fileMetadata);
                await dbContext.SaveChangesAsync();

                _logger.LogInformation("File uploaded successfully: {FileName} to {BucketName}/{ObjectKey}", 
                    fileName, bucketName, fullObjectPath);

                return fileMetadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName} to bucket {BucketName}", fileName, bucketName);
                throw;
            }
        }

        public async Task<Stream> DownloadFileAsync(FileMetadata fileMetadata)
        {
            return await DownloadFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
        }

        public async Task<Stream> DownloadFileAsync(string bucketName, string objectKey)
        {
            try
            {
                var memoryStream = new MemoryStream();
                
                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey)
                    .WithCallbackStream(stream => stream.CopyTo(memoryStream));

                await _minioClient.GetObjectAsync(getObjectArgs);
                memoryStream.Position = 0;

                _logger.LogInformation("File downloaded successfully: {BucketName}/{ObjectKey}", bucketName, objectKey);
                return memoryStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file {BucketName}/{ObjectKey}", bucketName, objectKey);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(FileMetadata fileMetadata)
        {
            try
            {
                // Delete from MinIO
                var success = await DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                
                if (success)
                {
                    // Soft delete metadata record
                    await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
                    var metadata = await dbContext.FileMetadata.FindAsync(fileMetadata.Id);
                    if (metadata != null)
                    {
                        metadata.IsDeleted = true;
                        metadata.DeletedAt = DateTime.UtcNow;
                        await dbContext.SaveChangesAsync();
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file metadata {FileId}", fileMetadata.Id);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string bucketName, string objectKey)
        {
            try
            {
                var removeObjectArgs = new RemoveObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey);

                await _minioClient.RemoveObjectAsync(removeObjectArgs);

                _logger.LogInformation("File deleted successfully: {BucketName}/{ObjectKey}", bucketName, objectKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {BucketName}/{ObjectKey}", bucketName, objectKey);
                return false;
            }
        }

        private string GenerateObjectKey(string fileName, string? folderPath = null)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            var extension = Path.GetExtension(fileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            
            return $"{nameWithoutExtension}_{timestamp}_{uniqueId}{extension}";
        }

        public async Task<bool> CreateFolderAsync(string bucketName, string folderPath)
        {
            try
            {
                // Ensure folder path ends with /
                var normalizedPath = folderPath.TrimEnd('/') + "/";

                // Create an empty object to represent the folder
                var emptyStream = new MemoryStream();
                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(normalizedPath)
                    .WithStreamData(emptyStream)
                    .WithObjectSize(0);

                await _minioClient.PutObjectAsync(putObjectArgs);

                _logger.LogInformation("Folder created successfully: {BucketName}/{FolderPath}", bucketName, normalizedPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating folder {BucketName}/{FolderPath}", bucketName, folderPath);
                return false;
            }
        }

        public async Task<IEnumerable<MinioObjectInfo>> ListFilesAsync(string bucketName, string? prefix = null)
        {
            try
            {
                var objects = new List<MinioObjectInfo>();
                var listObjectsArgs = new ListObjectsArgs()
                    .WithBucket(bucketName)
                    .WithPrefix(prefix)
                    .WithRecursive(true);

                await foreach (var item in _minioClient.ListObjectsEnumAsync(listObjectsArgs))
                {
                    objects.Add(new MinioObjectInfo
                    {
                        ObjectName = item.Key,
                        Size = item.Size,
                        LastModified = item.LastModifiedDateTime ?? DateTime.MinValue,
                        ETag = item.ETag ?? string.Empty,
                        ContentType = string.Empty // MinIO doesn't return content type in list
                    });
                }

                return objects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing files in bucket {BucketName} with prefix {Prefix}", bucketName, prefix);
                throw;
            }
        }

        public async Task<string> GetPresignedUrlAsync(FileMetadata fileMetadata, int expiryInSeconds = 3600)
        {
            try
            {
                var presignedGetObjectArgs = new PresignedGetObjectArgs()
                    .WithBucket(fileMetadata.BucketName)
                    .WithObject(fileMetadata.ObjectKey)
                    .WithExpiry(expiryInSeconds);

                var url = await _minioClient.PresignedGetObjectAsync(presignedGetObjectArgs);

                _logger.LogInformation("Generated presigned URL for {BucketName}/{ObjectKey}",
                    fileMetadata.BucketName, fileMetadata.ObjectKey);

                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned URL for {BucketName}/{ObjectKey}",
                    fileMetadata.BucketName, fileMetadata.ObjectKey);
                throw;
            }
        }

        public async Task<string> GetPresignedUploadUrlAsync(string bucketName, string objectKey, int expiryInSeconds = 3600)
        {
            try
            {
                var presignedPutObjectArgs = new PresignedPutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey)
                    .WithExpiry(expiryInSeconds);

                var url = await _minioClient.PresignedPutObjectAsync(presignedPutObjectArgs);

                _logger.LogInformation("Generated presigned upload URL for {BucketName}/{ObjectKey}", bucketName, objectKey);

                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned upload URL for {BucketName}/{ObjectKey}", bucketName, objectKey);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string bucketName, string objectKey)
        {
            try
            {
                var statObjectArgs = new StatObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey);

                await _minioClient.StatObjectAsync(statObjectArgs);
                return true;
            }
            catch (ObjectNotFoundException)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists {BucketName}/{ObjectKey}", bucketName, objectKey);
                throw;
            }
        }

        public async Task<MinioObjectInfo?> GetFileInfoAsync(string bucketName, string objectKey)
        {
            try
            {
                var statObjectArgs = new StatObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey);

                var stat = await _minioClient.StatObjectAsync(statObjectArgs);

                return new MinioObjectInfo
                {
                    ObjectName = stat.ObjectName,
                    Size = stat.Size,
                    LastModified = stat.LastModified,
                    ETag = stat.ETag,
                    ContentType = stat.ContentType,
                    Metadata = stat.MetaData?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
                };
            }
            catch (ObjectNotFoundException)
            {
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file info {BucketName}/{ObjectKey}", bucketName, objectKey);
                throw;
            }
        }

        public async Task<(bool IsValid, AllowedFileType? FileType, string? ErrorMessage)> ValidateFileTypeAsync(string fileName, string contentType)
        {
            try
            {
                var extension = Path.GetExtension(fileName).ToLowerInvariant();

                // Check if extension is allowed
                if (!FileStorageConstants.FileExtensions.AllowedExtensions.Contains(extension))
                {
                    return (false, null, $"File type '{extension}' is not allowed");
                }

                // Validate content type matches extension
                if (FileStorageConstants.FileExtensions.ContentTypeMap.TryGetValue(extension, out var expectedContentType))
                {
                    if (!string.Equals(contentType, expectedContentType, StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogWarning("Content type mismatch for file {FileName}. Expected: {Expected}, Actual: {Actual}",
                            fileName, expectedContentType, contentType);
                        // Allow but log warning - some browsers may send different content types
                    }
                }

                // Determine file type enum
                var fileType = extension switch
                {
                    ".jpg" or ".jpeg" => AllowedFileType.JPEG,
                    ".png" => AllowedFileType.PNG,
                    ".gif" => AllowedFileType.GIF,
                    ".bmp" => AllowedFileType.BMP,
                    ".webp" => AllowedFileType.WEBP,
                    ".pdf" => AllowedFileType.PDF,
                    ".txt" => AllowedFileType.TXT,
                    ".csv" => AllowedFileType.CSV,
                    ".doc" => AllowedFileType.DOC,
                    ".docx" => AllowedFileType.DOCX,
                    ".xls" => AllowedFileType.XLS,
                    ".xlsx" => AllowedFileType.XLSX,
                    _ => (AllowedFileType?)null
                };

                if (fileType == null)
                {
                    return (false, null, $"Unable to determine file type for extension '{extension}'");
                }

                return (true, fileType, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating file type for {FileName}", fileName);
                return (false, null, "Error validating file type");
            }
        }

        public async Task<bool> InitializeBucketsAsync()
        {
            try
            {
                var buckets = _config.Buckets.GetAllBuckets();
                var tasks = buckets.Select(async bucket =>
                {
                    try
                    {
                        await EnsureBucketExistsAsync(bucket);

                        // Enable versioning if configured
                        if (_config.Settings.EnableVersioning)
                        {
                            // Note: MinIO .NET client doesn't have direct versioning API
                            // This would typically be done via MinIO admin API or server configuration
                            _logger.LogInformation("Versioning should be enabled for bucket {BucketName} via server configuration", bucket);
                        }

                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to initialize bucket {BucketName}", bucket);
                        return false;
                    }
                });

                var results = await System.Threading.Tasks.Task.WhenAll(tasks);
                var allSuccessful = results.All(r => r);

                if (allSuccessful)
                {
                    _logger.LogInformation("All buckets initialized successfully");
                }
                else
                {
                    _logger.LogWarning("Some buckets failed to initialize");
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing buckets");
                return false;
            }
        }

        private async System.Threading.Tasks.Task EnsureBucketExistsAsync(string bucketName)
        {
            var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
            var exists = await _minioClient.BucketExistsAsync(bucketExistsArgs);

            if (!exists)
            {
                var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
                await _minioClient.MakeBucketAsync(makeBucketArgs);
                _logger.LogInformation("Created bucket: {BucketName}", bucketName);
            }
        }
    }
}
