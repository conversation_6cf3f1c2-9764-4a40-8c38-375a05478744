using Shared.Interfaces;
using Shared.Utils;
using Shared.Enums;

namespace Shared.GraphQL.Models;

public class Training : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public ICollection<Worker> Workers { get; set; } = new List<Worker>();
    public ICollection<WorkerTrainingHistory> TrainingHistory { get; set; } = new List<WorkerTrainingHistory>();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Duration { get; set; }
    public int? ValidityPeriodMonths { get; set; } // How long the training certification is valid
    public string? TrainingType { get; set; }
    public string? Trainer { get; set; }
    public string? Frequency { get; set; }
    public TrainingStatus Status { get; set; } = TrainingStatus.Scheduled;

    // Audit Fields
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }

    // Soft Delete Fields
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }

    public TrainingDuration? ParsedDuration => Duration != null ? TrainingDuration.Parse(Duration) : null;
}
