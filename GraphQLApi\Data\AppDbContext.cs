using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using System.Reflection;
using Shared.Interfaces;
using System.Linq.Expressions;

namespace GraphQLApi.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Worker> Workers { get; set; }
        public DbSet<WorkerAttendance> WorkerAttendances { get; set; }
        public DbSet<ToolboxSession> ToolboxSessions { get; set; }
        public DbSet<ToolboxAttendance> ToolboxAttendances { get; set; }
        public DbSet<Training> Trainings { get; set; }
        public DbSet<Trade> Trades { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<WorkerTrainingHistory> WorkerTrainingHistory { get; set; }
        public DbSet<Shared.GraphQL.Models.Task> Tasks { get; set; }
        public DbSet<Equipment> Equipment { get; set; }
        public DbSet<Incident> Incidents { get; set; }
        public DbSet<FileMetadata> FileMetadata { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Apply global query filters for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)
                        ?.MakeGenericMethod(entityType.ClrType);
                    var filter = method?.Invoke(null, Array.Empty<object>());
                    entityType.SetQueryFilter((LambdaExpression)filter!);
                }
            }

            base.OnModelCreating(modelBuilder);
        }

        private static LambdaExpression GetSoftDeleteFilter<TEntity>() where TEntity : class, ISoftDeletable
        {
            Expression<Func<TEntity, bool>> filter = x => !x.IsDeleted;
            return filter;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            HandleSoftDelete();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is IAuditableEntity && (
                    e.State == EntityState.Added ||
                    e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (IAuditableEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                    entity.CreatedBy ??= "System";
                }

                entity.UpdatedAt = DateTime.UtcNow;
                entity.UpdatedBy ??= "System";
            }
        }

        private void HandleSoftDelete()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is ISoftDeletable && e.State == EntityState.Deleted);

            foreach (var entityEntry in entries)
            {
                var entity = (ISoftDeletable)entityEntry.Entity;
                entityEntry.State = EntityState.Modified;
                entity.IsDeleted = true;
                entity.DeletedAt = DateTime.UtcNow;
                entity.DeletedBy ??= "System";

                // Cascade soft delete to related child entities
                CascadeSoftDelete(entityEntry, new HashSet<object>(), 0);
            }
        }

        private const int MaxCascadeDepth = 10;

        private void CascadeSoftDelete(
            Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry parentEntry,
            HashSet<object>? visited = null,
            int depth = 0)
        {
            if (depth > MaxCascadeDepth)
                return;

            visited ??= new HashSet<object>();
            var entity = parentEntry.Entity;

            // Prevent cycles
            if (!visited.Add(entity))
                return;

            var navigations = parentEntry.Navigations;

            foreach (var navigationEntry in navigations)
            {
                // Ensure navigation is loaded
                if (!navigationEntry.IsLoaded)
                {
                    navigationEntry.Load();
                }

                if (!navigationEntry.Metadata.IsCollection)
                {
                    var childEntity = navigationEntry.CurrentValue;
                    if (childEntity is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                    {
                        var childEntry = ChangeTracker.Entries()
                            .FirstOrDefault(e => e.Entity == childEntity);
                        if (childEntry != null)
                        {
                            childEntry.State = EntityState.Modified;
                            softDeletableChild.IsDeleted = true;
                            softDeletableChild.DeletedAt = DateTime.UtcNow;
                            softDeletableChild.DeletedBy ??= "System";
                            // Recursively cascade with updated parameters
                            CascadeSoftDelete(childEntry, visited, depth + 1);
                        }
                    }
                }
                else
                {
                    var children = navigationEntry.CurrentValue as System.Collections.IEnumerable;
                    if (children != null)
                    {
                        foreach (var child in children)
                        {
                            if (child is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                            {
                                var childEntry = ChangeTracker.Entries()
                                    .FirstOrDefault(e => e.Entity == child);
                                if (childEntry != null)
                                {
                                    childEntry.State = EntityState.Modified;
                                    softDeletableChild.IsDeleted = true;
                                    softDeletableChild.DeletedAt = DateTime.UtcNow;
                                    softDeletableChild.DeletedBy ??= "System";
                                    // Recursively cascade
                                    CascadeSoftDelete(childEntry);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}