namespace Shared.Enums
{
    /// <summary>
    /// Allowed file types for MinIO storage
    /// </summary>
    public enum AllowedFileType
    {
        // Image types
        JPEG,
        JPG,
        PNG,
        GIF,
        BMP,
        WEBP,
        
        // Document types
        PDF,
        
        // Microsoft Word
        DOC,
        DOCX,
        
        // Microsoft Excel
        XLS,
        XLSX,
        
        // Additional common types
        CSV,
        TXT
    }

    /// <summary>
    /// Predefined bucket names for different file categories
    /// </summary>
    public enum BucketName
    {
        PROFILE_PICTURE,
        CERTIFICATION,
        SIGNATURES,
        TEMP,
        DOCS
    }

    /// <summary>
    /// File storage operation results
    /// </summary>
    public enum FileStorageResult
    {
        SUCCESS,
        FILE_TOO_LARGE,
        INVALID_FILE_TYPE,
        BUCKET_NOT_FOUND,
        UPLOAD_FAILED,
        DOWNLOAD_FAILED,
        DELETE_FAILED,
        FILE_NOT_FOUND
    }
}
